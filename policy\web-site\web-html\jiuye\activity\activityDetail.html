<!DOCTYPE html>
<html>
<head>
    <meta name="keywords" content="青创通 · 青岛市创业服务云平台">
    <meta name="description" content="">
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE11">
    <meta http-equiv="Content-Type"
        content="text/html; charset=UTF-8; X-Content-Type-Options=nosniff; X-XSS-Protection: 1;mode=block" />
    <title>培训订单详情-青创通 · 青岛市创业服务云平台</title>
    <!-- base css -->
    <link rel="stylesheet" type="text/css" href="../public/css/zh.min.css" />
    <!-- jbox css -->
    <link rel="stylesheet" type="text/css" href="../public/plugins/jbox/Skins/Blue/jbox.css" />
    <!-- common css -->
    <link rel="stylesheet" type="text/css" href="../public/css/common.css" />
    <!-- this page css -->
    <link rel="stylesheet" type="text/css" href="css/activityDetail.css?v=202502281010" />
    <link rel="shortcut icon" href="../public/images/icons/favicon.ico" type="image/x-icon" />
    <style>
        .training-order-detail {
            background: #fff;
            border-radius: 8px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .detail-header {
            border-bottom: 2px solid #eee;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .order-title {
            font-size: 28px;
            color: #333;
            margin-bottom: 15px;
            font-weight: bold;
        }
        .order-meta {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        .order-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
        }
        .status-published { background: #e7f5e7; color: #28a745; }
        .status-ongoing { background: #fff3cd; color: #856404; }
        .status-completed { background: #d1ecf1; color: #0c5460; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
        .order-fee {
            font-size: 24px;
            color: #ff6b35;
            font-weight: bold;
        }
        .detail-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }
        .detail-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 8px;
        }
        .detail-section h3 {
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
            font-size: 18px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        .info-item {
            display: flex;
            align-items: center;
        }
        .info-item label {
            font-weight: bold;
            color: #666;
            min-width: 100px;
        }
        .description-content, .requirements-content, .certificate-content {
            line-height: 1.6;
            color: #555;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .action-panel {
            position: sticky;
            top: 20px;
            background: #fff;
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .action-panel h4 {
            margin-bottom: 15px;
            color: #333;
        }
        .btn {
            display: block;
            width: 100%;
            padding: 12px;
            margin-bottom: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            text-align: center;
            text-decoration: none;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background: #0056b3;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        .related-orders {
            background: #fff;
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 20px;
        }
        .related-orders h4 {
            margin-bottom: 15px;
            color: #333;
        }
        .breadcrumb-nav {
            margin-bottom: 20px;
            color: #666;
        }
        .breadcrumb-nav a {
            color: #007bff;
            text-decoration: none;
        }
        .breadcrumb-nav a:hover {
            text-decoration: underline;
        }
        @media (max-width: 768px) {
            .detail-content {
                grid-template-columns: 1fr;
            }
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body>
    <div id="headerBar"></div>

    <!-- main start -->
    <div class="pageBg">
        <div class="conAuto2">
            <!-- 面包屑导航 -->
            <div class="breadcrumb-nav">
                <a href="index.html">首页</a> >
                <a href="trainingOrderList.html">培训订单</a> >
                <span>订单详情</span>
            </div>

            <div class="training-order-detail">
                <div class="detail-header">
                    <h1 class="order-title" id="orderTitle">加载中...</h1>
                    <div class="order-meta">
                        <span class="order-status" id="orderStatus">--</span>
                        <span class="order-fee" id="orderFee">--</span>
                        <span class="order-participants" id="orderParticipants">--</span>
                    </div>
                </div>

                <div class="detail-content">
                    <div class="content-left">
                        <!-- 培训信息 -->
                        <div class="detail-section">
                            <h3>培训信息</h3>
                            <div class="info-grid">
                                <div class="info-item">
                                    <label>培训类型：</label>
                                    <span id="trainingType">--</span>
                                </div>
                                <div class="info-item">
                                    <label>培训分类：</label>
                                    <span id="trainingCategory">--</span>
                                </div>
                                <div class="info-item">
                                    <label>培训级别：</label>
                                    <span id="trainingLevel">--</span>
                                </div>
                                <div class="info-item">
                                    <label>培训时长：</label>
                                    <span id="trainingDuration">--</span>
                                </div>
                                <div class="info-item">
                                    <label>培训地址：</label>
                                    <span id="trainingAddress">--</span>
                                </div>
                                <div class="info-item">
                                    <label>最大参与人数：</label>
                                    <span id="maxParticipants">--</span>
                                </div>
                            </div>
                        </div>
                    
                    <div class="detail-section">
                        <h3>时间安排</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>开始时间：</label>
                                <span data-bind="text:orderDetail().startDate||'--'"></span>
                            </div>
                            <div class="info-item">
                                <label>结束时间：</label>
                                <span data-bind="text:orderDetail().endDate||'--'"></span>
                            </div>
                            <div class="info-item">
                                <label>报名截止：</label>
                                <span data-bind="text:orderDetail().registrationDeadline||'--'"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="detail-section">
                        <h3>培训描述</h3>
                        <p class="order-description" data-bind="text:orderDetail().orderDescription||'暂无描述'"></p>
                    </div>
                    
                    <div class="detail-section">
                        <h3>报名要求</h3>
                        <p class="order-requirements" data-bind="text:orderDetail().requirements||'无特殊要求'"></p>
                    </div>
                    
                    <div class="detail-section">
                        <h3>联系方式</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>联系人：</label>
                                <span data-bind="text:orderDetail().contactPerson||'--'"></span>
                            </div>
                            <div class="info-item">
                                <label>联系电话：</label>
                                <span data-bind="text:orderDetail().contactPhone||'--'"></span>
                            </div>
                            <div class="info-item">
                                <label>培训地址：</label>
                                <span data-bind="text:orderDetail().trainingAddress||'--'"></span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="detail-actions">
                    <button class="btn-primary" data-bind="click:handleRegister, visible:canRegister()">立即报名</button>
                    <button class="btn-secondary" onclick="history.back()">返回</button>
                </div>
            </div>
        </div>
    </div>
    <!-- main end -->
    
    <!--jquery js-->
    <script src="../public/js/jquery-3.5.0.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/knockout.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/utils.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/common.js" type="text/javascript" charset="utf-8"></script>
    
    <script>
        // 公用模块html
        footerBar()
        
        var viewModel = {
            orderDetail: ko.observable({}),
            canRegister: ko.computed(function() {
                var order = viewModel.orderDetail();
                return order.orderStatus === '1' && 
                       (order.currentParticipants || 0) < (order.maxParticipants || 0);
            }),
            getStatusText: function(status) {
                var statusMap = {
                    '0': '草稿',
                    '1': '发布',
                    '2': '进行中',
                    '3': '已完成',
                    '4': '已取消'
                };
                return statusMap[status] || '未知';
            },
            handleRegister: function() {
                alert('报名功能待开发');
            }
        };
        
        // 获取URL参数
        function getUrlParam(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]);
            return null;
        }
        
        // 自定义Ajax请求函数
        function customAjaxRequest(url, params, callback) {
            var baseUrl = 'http://localhost:80/sux-admin/';

            $.ajax({
                url: baseUrl + url,
                type: 'GET',
                data: params,
                dataType: 'json',
                timeout: 30000,
                success: function(response) {
                    if (callback && typeof callback === 'function') {
                        callback(response);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('请求失败:', error);
                    if (callback && typeof callback === 'function') {
                        callback({
                            code: -1,
                            msg: '请求失败: ' + error,
                            data: null
                        });
                    }
                }
            });
        }

        // 获取订单详情
        function getOrderDetail() {
            var orderId = getUrlParam('id');
            if (!orderId) {
                alert('订单ID不存在');
                return;
            }

            customAjaxRequest('public/training/order/' + orderId, {}, function(data){
                if(data.code == 0 || data.code == 200) {
                    var orderData = data.data || data.obj || data;
                    if(orderData && orderData.orderId) {
                        viewModel.orderDetail(orderData);
                    } else {
                        alert('订单数据格式错误');
                    }
                } else {
                    alert('获取订单详情失败：' + (data.msg || data.message || '未知错误'));
                }
            });
        }
        
        // 初始化
        getOrderDetail();
        ko.applyBindings(viewModel, document.getElementById("viewModelBox"));
    </script>
</body>
</html>
