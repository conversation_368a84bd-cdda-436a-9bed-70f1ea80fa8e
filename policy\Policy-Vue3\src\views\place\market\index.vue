<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="市场名称" prop="marketName">
        <el-input
          v-model="queryParams.marketName"
          placeholder="请输入市场名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="市场类型" prop="marketType">
        <el-select v-model="queryParams.marketType" placeholder="请选择市场类型" clearable>
          <el-option label="综合市场" value="综合市场" />
          <el-option label="专业市场" value="专业市场" />
          <el-option label="临时市场" value="临时市场" />
        </el-select>
      </el-form-item>
      <el-form-item label="区域" prop="regionCode">
        <el-select v-model="queryParams.regionCode" placeholder="请选择区域" clearable>
          <el-option
            v-for="region in regionOptions"
            :key="region.regionCode"
            :label="region.regionName"
            :value="region.regionCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="正常" value="0" />
          <el-option label="停用" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['place:market:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['place:market:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['place:market:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['place:market:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="marketInfoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="市场ID" align="center" prop="marketId" />
      <el-table-column label="市场名称" align="center" prop="marketName" :show-overflow-tooltip="true" />
      <el-table-column label="市场编码" align="center" prop="marketCode" />
      <el-table-column label="市场类型" align="center" prop="marketType" />
      <el-table-column label="区域" align="center" prop="regionName" />
      <el-table-column label="联系人" align="center" prop="contactPerson" />
      <el-table-column label="联系电话" align="center" prop="contactPhone" />
      <el-table-column label="零工容纳量" align="center" prop="workerCapacity" />
      <el-table-column label="当前零工数" align="center" prop="currentWorkerCount" />
      <el-table-column label="日均需求" align="center" prop="dailyAvgDemand" />
      <el-table-column label="管理费用" align="center" prop="managementFee" />
      <el-table-column label="是否推荐" align="center" prop="isFeatured">
        <template #default="scope">
          <dict-tag :options="sys_yes_no" :value="scope.row.isFeatured"/>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="浏览次数" align="center" prop="viewCount" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['place:market:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['place:market:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['place:market:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改零工市场基础信息对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="marketInfoRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="市场名称" prop="marketName">
              <el-input v-model="form.marketName" placeholder="请输入市场名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="市场编码" prop="marketCode">
              <el-input v-model="form.marketCode" placeholder="请输入市场编码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="市场类型" prop="marketType">
              <el-select v-model="form.marketType" placeholder="请选择市场类型">
                <el-option label="综合市场" value="综合市场" />
                <el-option label="专业市场" value="专业市场" />
                <el-option label="临时市场" value="临时市场" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="营业时间" prop="operatingHours">
              <el-input v-model="form.operatingHours" placeholder="请输入营业时间" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="市场地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入市场地址" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="区域代码" prop="regionCode">
              <el-input v-model="form.regionCode" placeholder="请输入区域代码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="区域名称" prop="regionName">
              <el-input v-model="form.regionName" placeholder="请输入区域名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="联系人" prop="contactPerson">
              <el-input v-model="form.contactPerson" placeholder="请输入联系人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="联系邮箱" prop="contactEmail">
          <el-input v-model="form.contactEmail" placeholder="请输入联系邮箱" />
        </el-form-item>
        <el-row>
          <el-col :span="8">
            <el-form-item label="零工容纳量" prop="workerCapacity">
              <el-input-number v-model="form.workerCapacity" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="当前零工数" prop="currentWorkerCount">
              <el-input-number v-model="form.currentWorkerCount" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="日均需求" prop="dailyAvgDemand">
              <el-input-number v-model="form.dailyAvgDemand" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="管理费用" prop="managementFee">
              <el-input v-model="form.managementFee" placeholder="元/人/天" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务费率" prop="serviceFeeRate">
              <el-input v-model="form.serviceFeeRate" placeholder="%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="用工高峰时段" prop="peakDemandTime">
          <el-input v-model="form.peakDemandTime" placeholder="请输入用工高峰时段" />
        </el-form-item>
        <el-form-item label="安全措施" prop="safetyMeasures">
          <el-input v-model="form.safetyMeasures" type="textarea" placeholder="请输入安全措施描述" />
        </el-form-item>
        <el-form-item label="市场描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入市场详细描述" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否推荐" prop="isFeatured">
              <el-radio-group v-model="form.isFeatured">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio label="0">正常</el-radio>
                <el-radio label="1">停用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 市场详情对话框 -->
    <el-dialog title="零工市场详情" v-model="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="市场名称">{{ detailForm.marketName }}</el-descriptions-item>
        <el-descriptions-item label="市场编码">{{ detailForm.marketCode }}</el-descriptions-item>
        <el-descriptions-item label="市场类型">{{ detailForm.marketType }}</el-descriptions-item>
        <el-descriptions-item label="营业时间">{{ detailForm.operatingHours }}</el-descriptions-item>
        <el-descriptions-item label="市场地址" :span="2">{{ detailForm.address }}</el-descriptions-item>
        <el-descriptions-item label="联系人">{{ detailForm.contactPerson }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ detailForm.contactPhone }}</el-descriptions-item>
        <el-descriptions-item label="联系邮箱" :span="2">{{ detailForm.contactEmail }}</el-descriptions-item>
        <el-descriptions-item label="零工容纳量">{{ detailForm.workerCapacity }}</el-descriptions-item>
        <el-descriptions-item label="当前零工数量">{{ detailForm.currentWorkerCount }}</el-descriptions-item>
        <el-descriptions-item label="日均用工需求">{{ detailForm.dailyAvgDemand }}</el-descriptions-item>
        <el-descriptions-item label="用工高峰时段">{{ detailForm.peakDemandTime }}</el-descriptions-item>
        <el-descriptions-item label="管理费用">{{ detailForm.managementFee }} 元/人/天</el-descriptions-item>
        <el-descriptions-item label="服务费率">{{ detailForm.serviceFeeRate }}%</el-descriptions-item>
        <el-descriptions-item label="浏览次数">{{ detailForm.viewCount }}</el-descriptions-item>
        <el-descriptions-item label="安全措施" :span="2">{{ detailForm.safetyMeasures }}</el-descriptions-item>
        <el-descriptions-item label="市场描述" :span="2">{{ detailForm.description }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup name="LaborMarketInfo">
import { listLaborMarketInfo, getLaborMarketInfo, delLaborMarketInfo, addLaborMarketInfo, updateLaborMarketInfo } from "@/api/place/market";

const { proxy } = getCurrentInstance();
const { sys_normal_disable, sys_yes_no } = proxy.useDict('sys_normal_disable', 'sys_yes_no');

const marketInfoList = ref([]);
const open = ref(false);
const detailOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 选项数据
const regionOptions = ref([]);

const data = reactive({
  form: {},
  detailForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    marketName: null,
    marketCode: null,
    marketType: null,
    regionCode: null,
    status: null,
  },
  rules: {
    marketName: [
      { required: true, message: "市场名称不能为空", trigger: "blur" }
    ],
    marketType: [
      { required: true, message: "市场类型不能为空", trigger: "change" }
    ],
    address: [
      { required: true, message: "市场地址不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, detailForm, rules } = toRefs(data);

/** 查询零工市场基础信息列表 */
function getList() {
  loading.value = true;
  listLaborMarketInfo(queryParams.value).then(response => {
    marketInfoList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    marketId: null,
    marketName: null,
    marketCode: null,
    marketType: null,
    address: null,
    regionCode: null,
    regionName: null,
    contactPerson: null,
    contactPhone: null,
    contactEmail: null,
    operatingHours: null,
    workerCapacity: 0,
    currentWorkerCount: 0,
    dailyAvgDemand: 0,
    peakDemandTime: null,
    managementFee: null,
    serviceFeeRate: null,
    safetyMeasures: null,
    description: null,
    status: "0",
    isFeatured: 0,
    remark: null
  };
  proxy.resetForm("marketInfoRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.marketId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加零工市场基础信息";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _marketId = row.marketId || ids.value
  getLaborMarketInfo(_marketId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改零工市场基础信息";
  });
}

/** 详情按钮操作 */
function handleDetail(row) {
  const _marketId = row.marketId;
  getLaborMarketInfo(_marketId).then(response => {
    detailForm.value = response.data;
    detailOpen.value = true;
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["marketInfoRef"].validate(valid => {
    if (valid) {
      if (form.value.marketId != null) {
        updateLaborMarketInfo(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addLaborMarketInfo(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _marketIds = row.marketId || ids.value;
  proxy.$modal.confirm('是否确认删除零工市场基础信息编号为"' + _marketIds + '"的数据项？').then(function() {
    return delLaborMarketInfo(_marketIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('place/market/export', {
    ...queryParams.value
  }, `labor_market_info_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
  // 加载选项数据
  regionOptions.value = [
    { regionCode: '370202', regionName: '市南区' },
    { regionCode: '370203', regionName: '市北区' },
    { regionCode: '370212', regionName: '崂山区' }
  ];
});
</script>
