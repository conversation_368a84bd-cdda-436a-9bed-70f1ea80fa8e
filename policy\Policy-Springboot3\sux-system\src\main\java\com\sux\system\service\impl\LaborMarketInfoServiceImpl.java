package com.sux.system.service.impl;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sux.common.utils.DateUtils;
import com.sux.common.utils.SecurityUtils;
import com.sux.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sux.system.mapper.LaborMarketInfoMapper;
import com.sux.system.domain.LaborMarketInfo;
import com.sux.system.service.ILaborMarketInfoService;

/**
 * 零工市场基础信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class LaborMarketInfoServiceImpl extends ServiceImpl<LaborMarketInfoMapper, LaborMarketInfo> implements ILaborMarketInfoService
{
    @Autowired
    private LaborMarketInfoMapper laborMarketInfoMapper;

    /**
     * 查询零工市场基础信息列表
     * 
     * @param laborMarketInfo 零工市场基础信息
     * @return 零工市场基础信息
     */
    @Override
    public List<LaborMarketInfo> selectLaborMarketInfoList(LaborMarketInfo laborMarketInfo)
    {
        return laborMarketInfoMapper.selectLaborMarketInfoList(laborMarketInfo);
    }

    /**
     * 查询零工市场基础信息
     * 
     * @param marketId 零工市场基础信息主键
     * @return 零工市场基础信息
     */
    @Override
    public LaborMarketInfo selectLaborMarketInfoByMarketId(Long marketId)
    {
        return laborMarketInfoMapper.selectLaborMarketInfoByMarketId(marketId);
    }

    /**
     * 新增零工市场基础信息
     * 
     * @param laborMarketInfo 零工市场基础信息
     * @return 结果
     */
    @Override
    public int insertLaborMarketInfo(LaborMarketInfo laborMarketInfo)
    {
        laborMarketInfo.setCreateId(SecurityUtils.getUserId());
        laborMarketInfo.setCreateTime(DateUtils.getNowDate());
        
        // 设置默认值
        if (StringUtils.isEmpty(laborMarketInfo.getStatus())) {
            laborMarketInfo.setStatus("0");
        }
        if (laborMarketInfo.getIsFeatured() == null) {
            laborMarketInfo.setIsFeatured(0);
        }
        if (laborMarketInfo.getWorkerCapacity() == null) {
            laborMarketInfo.setWorkerCapacity(0);
        }
        if (laborMarketInfo.getCurrentWorkerCount() == null) {
            laborMarketInfo.setCurrentWorkerCount(0);
        }
        if (laborMarketInfo.getDailyAvgDemand() == null) {
            laborMarketInfo.setDailyAvgDemand(0);
        }
        if (laborMarketInfo.getViewCount() == null) {
            laborMarketInfo.setViewCount(0);
        }
        if (laborMarketInfo.getSortOrder() == null) {
            laborMarketInfo.setSortOrder(0);
        }
        
        return laborMarketInfoMapper.insertLaborMarketInfo(laborMarketInfo);
    }

    /**
     * 修改零工市场基础信息
     * 
     * @param laborMarketInfo 零工市场基础信息
     * @return 结果
     */
    @Override
    public int updateLaborMarketInfo(LaborMarketInfo laborMarketInfo)
    {
        laborMarketInfo.setUpdateId(SecurityUtils.getUserId());
        laborMarketInfo.setUpdateTime(DateUtils.getNowDate());
        return laborMarketInfoMapper.updateLaborMarketInfo(laborMarketInfo);
    }

    /**
     * 批量删除零工市场基础信息
     * 
     * @param marketIds 需要删除的零工市场基础信息主键
     * @return 结果
     */
    @Override
    public int deleteLaborMarketInfoByMarketIds(Long[] marketIds)
    {
        return laborMarketInfoMapper.deleteLaborMarketInfoByMarketIds(marketIds);
    }

    /**
     * 删除零工市场基础信息信息
     * 
     * @param marketId 零工市场基础信息主键
     * @return 结果
     */
    @Override
    public int deleteLaborMarketInfoByMarketId(Long marketId)
    {
        return laborMarketInfoMapper.deleteLaborMarketInfoByMarketId(marketId);
    }

    /**
     * 查询推荐零工市场信息列表
     * 
     * @param laborMarketInfo 零工市场基础信息
     * @return 零工市场基础信息集合
     */
    @Override
    public List<LaborMarketInfo> selectFeaturedLaborMarketInfoList(LaborMarketInfo laborMarketInfo)
    {
        return laborMarketInfoMapper.selectFeaturedLaborMarketInfoList(laborMarketInfo);
    }

    /**
     * 查询活跃零工市场信息列表
     * 
     * @param laborMarketInfo 零工市场基础信息
     * @return 零工市场基础信息集合
     */
    @Override
    public List<LaborMarketInfo> selectActiveLaborMarketInfoList(LaborMarketInfo laborMarketInfo)
    {
        return laborMarketInfoMapper.selectActiveLaborMarketInfoList(laborMarketInfo);
    }

    /**
     * 根据市场类型查询零工市场信息列表
     * 
     * @param marketType 市场类型
     * @return 零工市场基础信息集合
     */
    @Override
    public List<LaborMarketInfo> selectLaborMarketInfoByType(String marketType)
    {
        return laborMarketInfoMapper.selectLaborMarketInfoByType(marketType);
    }

    /**
     * 根据区域代码查询零工市场信息列表
     * 
     * @param regionCode 区域代码
     * @return 零工市场基础信息集合
     */
    @Override
    public List<LaborMarketInfo> selectLaborMarketInfoByRegion(String regionCode)
    {
        return laborMarketInfoMapper.selectLaborMarketInfoByRegion(regionCode);
    }

    /**
     * 根据服务类别查询零工市场信息列表
     * 
     * @param serviceCategory 服务类别
     * @return 零工市场基础信息集合
     */
    @Override
    public List<LaborMarketInfo> selectLaborMarketInfoByServiceCategory(String serviceCategory)
    {
        return laborMarketInfoMapper.selectLaborMarketInfoByServiceCategory(serviceCategory);
    }

    /**
     * 查询零工市场统计信息
     * 
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectLaborMarketInfoStatistics()
    {
        return laborMarketInfoMapper.selectLaborMarketInfoStatistics();
    }

    /**
     * 根据关键词搜索零工市场信息
     * 
     * @param keyword 关键词
     * @return 零工市场基础信息集合
     */
    @Override
    public List<LaborMarketInfo> selectLaborMarketInfoByKeyword(String keyword)
    {
        return laborMarketInfoMapper.selectLaborMarketInfoByKeyword(keyword);
    }

    /**
     * 更新零工市场浏览次数
     * 
     * @param marketId 市场ID
     * @return 结果
     */
    @Override
    public int updateLaborMarketInfoViewCount(Long marketId)
    {
        return laborMarketInfoMapper.updateLaborMarketInfoViewCount(marketId);
    }

    /**
     * 查询零工市场详细信息（包含关联信息）
     * 
     * @param marketId 市场ID
     * @return 零工市场基础信息
     */
    @Override
    public LaborMarketInfo selectLaborMarketInfoDetailByMarketId(Long marketId)
    {
        return laborMarketInfoMapper.selectLaborMarketInfoDetailByMarketId(marketId);
    }

    /**
     * 获取所有市场类型列表
     * 
     * @return 市场类型列表
     */
    @Override
    public List<String> selectAllMarketTypes()
    {
        return laborMarketInfoMapper.selectAllMarketTypes();
    }

    /**
     * 获取所有区域列表
     * 
     * @return 区域列表
     */
    @Override
    public List<Map<String, String>> selectAllRegions()
    {
        return laborMarketInfoMapper.selectAllRegions();
    }

    /**
     * 获取所有服务类别列表
     * 
     * @return 服务类别列表
     */
    @Override
    public List<String> selectAllServiceCategories()
    {
        return laborMarketInfoMapper.selectAllServiceCategories();
    }

    /**
     * 根据容量范围查询零工市场信息
     * 
     * @param minCapacity 最小容量
     * @param maxCapacity 最大容量
     * @return 零工市场基础信息集合
     */
    @Override
    public List<LaborMarketInfo> selectLaborMarketInfoByCapacityRange(Integer minCapacity, Integer maxCapacity)
    {
        return laborMarketInfoMapper.selectLaborMarketInfoByCapacityRange(minCapacity, maxCapacity);
    }

    /**
     * 根据费用范围查询零工市场信息
     * 
     * @param minFee 最小费用
     * @param maxFee 最大费用
     * @return 零工市场基础信息集合
     */
    @Override
    public List<LaborMarketInfo> selectLaborMarketInfoByFeeRange(java.math.BigDecimal minFee, java.math.BigDecimal maxFee)
    {
        return laborMarketInfoMapper.selectLaborMarketInfoByFeeRange(minFee, maxFee);
    }

    /**
     * 查询高需求零工市场（根据日均用工需求排序）
     * 
     * @param limit 限制数量
     * @return 零工市场基础信息集合
     */
    @Override
    public List<LaborMarketInfo> selectHighDemandLaborMarketInfo(Integer limit)
    {
        return laborMarketInfoMapper.selectHighDemandLaborMarketInfo(limit);
    }
}
