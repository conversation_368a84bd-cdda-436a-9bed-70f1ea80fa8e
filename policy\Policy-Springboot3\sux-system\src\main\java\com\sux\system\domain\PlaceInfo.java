package com.sux.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sux.common.annotation.Excel;
import com.sux.common.core.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 场地信息对象 place_info
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
@TableName("place_info")
public class PlaceInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 场地ID */
    @TableId(type = IdType.AUTO)
    private Long placeId;

    /** 场地名称 */
    @Excel(name = "场地名称")
    @NotBlank(message = "场地名称不能为空")
    @Size(min = 2, max = 200, message = "场地名称长度必须介于 2 和 200 之间")
    private String placeName;

    /** 场地编码 */
    @Excel(name = "场地编码")
    @Size(min = 0, max = 50, message = "场地编码不能超过50个字符")
    private String placeCode;

    /** 场地类型（创业园区/孵化器/众创空间/产业园等） */
    @Excel(name = "场地类型")
    @NotBlank(message = "场地类型不能为空")
    @Size(min = 0, max = 50, message = "场地类型不能超过50个字符")
    private String placeType;

    /** 场地等级（国家级/省级/市级/区级） */
    @Excel(name = "场地等级")
    @Size(min = 0, max = 50, message = "场地等级不能超过50个字符")
    private String placeLevel;

    /** 场地面积（平方米） */
    @Excel(name = "场地面积")
    private BigDecimal placeArea;

    /** 可使用面积（平方米） */
    @Excel(name = "可使用面积")
    private BigDecimal usableArea;

    /** 详细地址 */
    @Excel(name = "详细地址")
    @NotBlank(message = "详细地址不能为空")
    @Size(min = 0, max = 500, message = "详细地址不能超过500个字符")
    private String address;

    /** 区域代码 */
    @Excel(name = "区域代码")
    @Size(min = 0, max = 20, message = "区域代码不能超过20个字符")
    private String regionCode;

    /** 区域名称 */
    @Excel(name = "区域名称")
    @Size(min = 0, max = 100, message = "区域名称不能超过100个字符")
    private String regionName;

    /** 经度 */
    @Excel(name = "经度")
    private BigDecimal longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    private BigDecimal latitude;

    /** 联系人 */
    @Excel(name = "联系人")
    @Size(min = 0, max = 100, message = "联系人不能超过100个字符")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @Size(min = 0, max = 20, message = "联系电话不能超过20个字符")
    private String contactPhone;

    /** 联系邮箱 */
    @Excel(name = "联系邮箱")
    @Size(min = 0, max = 100, message = "联系邮箱不能超过100个字符")
    private String contactEmail;

    /** 已入驻企业数量 */
    @Excel(name = "已入驻企业数量")
    private Integer companyCount;

    /** 可提供工位数 */
    @Excel(name = "可提供工位数")
    private Integer availablePositions;

    /** 已占用工位数 */
    @Excel(name = "已占用工位数")
    private Integer occupiedPositions;

    /** 最低租金（元/月/平方米） */
    @Excel(name = "最低租金")
    private BigDecimal rentPriceMin;

    /** 最高租金（元/月/平方米） */
    @Excel(name = "最高租金")
    private BigDecimal rentPriceMax;

    /** 运营模式（自营/委托运营/合作运营） */
    @Excel(name = "运营模式")
    @Size(min = 0, max = 50, message = "运营模式不能超过50个字符")
    private String operationMode;

    /** 行业方向（多个用逗号分隔） */
    @Excel(name = "行业方向")
    @Size(min = 0, max = 200, message = "行业方向不能超过200个字符")
    private String industryDirection;

    /** 服务设施（JSON格式存储） */
    @Excel(name = "服务设施")
    private String serviceFacilities;

    /** 优惠政策描述 */
    @Excel(name = "优惠政策描述")
    private String preferentialPolicies;

    /** 招商开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "招商开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date applyStartDate;

    /** 招商结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "招商结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date applyEndDate;

    /** 招商时间状态（0长期 1定期） */
    @Excel(name = "招商时间状态", readConverterExp = "0=长期,1=定期")
    private Integer applyTimeStatus;

    /** 是否开放入驻（0否 1是） */
    @Excel(name = "是否开放入驻", readConverterExp = "0=否,1=是")
    private Integer isOpenSettle;

    /** 场地主图片URL */
    @Excel(name = "场地主图片URL")
    @Size(min = 0, max = 500, message = "场地主图片URL不能超过500个字符")
    private String imageUrl;

    /** 场地图片集（JSON格式存储多张图片URL） */
    @Excel(name = "场地图片集")
    private String imageGallery;

    /** 场地详细描述 */
    @Excel(name = "场地详细描述")
    private String description;

    /** 场地公告详情 */
    @Excel(name = "场地公告详情")
    private String noticeDetail;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 是否推荐（0否 1是） */
    @Excel(name = "是否推荐", readConverterExp = "0=否,1=是")
    private Integer isFeatured;

    /** 排序号 */
    @Excel(name = "排序号")
    private Integer sortOrder;

    /** 浏览次数 */
    @Excel(name = "浏览次数")
    private Integer viewCount;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("placeId", getPlaceId())
            .append("placeName", getPlaceName())
            .append("placeCode", getPlaceCode())
            .append("placeType", getPlaceType())
            .append("placeLevel", getPlaceLevel())
            .append("placeArea", getPlaceArea())
            .append("usableArea", getUsableArea())
            .append("address", getAddress())
            .append("regionCode", getRegionCode())
            .append("regionName", getRegionName())
            .append("longitude", getLongitude())
            .append("latitude", getLatitude())
            .append("contactPerson", getContactPerson())
            .append("contactPhone", getContactPhone())
            .append("contactEmail", getContactEmail())
            .append("companyCount", getCompanyCount())
            .append("availablePositions", getAvailablePositions())
            .append("occupiedPositions", getOccupiedPositions())
            .append("rentPriceMin", getRentPriceMin())
            .append("rentPriceMax", getRentPriceMax())
            .append("operationMode", getOperationMode())
            .append("industryDirection", getIndustryDirection())
            .append("serviceFacilities", getServiceFacilities())
            .append("preferentialPolicies", getPreferentialPolicies())
            .append("applyStartDate", getApplyStartDate())
            .append("applyEndDate", getApplyEndDate())
            .append("applyTimeStatus", getApplyTimeStatus())
            .append("isOpenSettle", getIsOpenSettle())
            .append("imageUrl", getImageUrl())
            .append("imageGallery", getImageGallery())
            .append("description", getDescription())
            .append("noticeDetail", getNoticeDetail())
            .append("status", getStatus())
            .append("isFeatured", getIsFeatured())
            .append("sortOrder", getSortOrder())
            .append("viewCount", getViewCount())
            .append("createId", getCreateId())
            .append("createTime", getCreateTime())
            .append("updateId", getUpdateId())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .append("remark", getRemark())
            .toString();
    }
}
